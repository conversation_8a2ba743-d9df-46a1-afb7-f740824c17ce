--- 
# - name: Installing dependencies
#   apt:
#     name: "python3-pip"
#     state: present

# - name: Getting Prometheus custom resource
#   shell: kubectl --kubeconfig {{ kubeconfig_location }} get prometheus -n monitoring -o yaml > /tmp/prometheus.yaml

# - name: Copying Thanos sidecar manifest
#   template:
#     src: thanos_sidecar.yaml
#     dest: /tmp/thanos_sidecar.yaml

# - name: "Removing output file"
#   file: 
#     path: /tmp/output.yaml
#     state: absent

# - name: Installing virtualenv
#   pip:
#     name: virtualenv
#     executable: /usr/bin/pip3

# - name: Creating virutalenv
#   shell: virtualenv /tmp/thanos

# - name: Copying script
#   copy:
#     src: insert_thanos_sidecar.py
#     dest: /tmp/thanos/

# - name: Installing PyYAml    
#   pip:
#     name: pyyaml
#     virtualenv: /tmp/thanos

# - name: "Activate project"
#   shell: source /tmp/thanos/bin/activate
#   args: 
#     executable: /bin/bash

# - name: Running script to insert sidecar into Prometheus resource
#   command: python3 /tmp/thanos/insert_thanos_sidecar.py

# - name: Applying Prometheus resource with Thanos sidecar
#   shell: kubectl --kubeconfig {{ kubeconfig_location }} apply -f /tmp/output.yaml

# - name: Copying Thanos sidecar service
#   copy: 
#     src: thanos_sidecar_loadbalancer.yaml
#     dest: /tmp/

# - name: Creating Thanos sidecar service
#   shell: kubectl --kubeconfig {{ kubeconfig_location }} apply -f /tmp/thanos_sidecar_loadbalancer.yaml

# - name: Create Thanos Object Storage
#   include: thanos_obj_storage.yml
#   when: thanos_use_obj_storage

- name: Installing Thanos binary
  unarchive:
    src: https://github.com/thanos-io/thanos/releases/download/v{{ thanos_release }}/{{ thanos_release_packet }}
    dest: /tmp
    remote_src: true

- name: Copying binary file
  command: mv /tmp/{{ thanos_release_packet[:-7] }}/thanos /usr/local/bin/
  

- name: Copying Thanos Sidecar service
  copy:
    src: thanos/thanos-sidecar.service
    dest: /etc/systemd/system/

- name: Enabling Thanos Sidecar service on startup
  systemd:
    name: thanos-sidecar.service
    enabled: yes
    state: restarted

