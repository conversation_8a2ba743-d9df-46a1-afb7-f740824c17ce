---
# Simple Kernel Fix Playbook - The Ansible Way
# Usage: ansible-playbook -i kernel_fix_inventory.ini kernel_fix_simple.yml --limit batch_01

- name: Apply Kernel Fix to Ubuntu 22.04 Systems
  hosts: 
  - production

  gather_facts: yes
  become: yes
  serial: 1   # Process one at a time (or use 10 for batches)
  
  vars:
    target_kernel: "5.15.0-143"
    ansible_host_key_checking: false
    
  tasks:
    - name: Check if Ubuntu 22.04
      assert:
        that:
          - ansible_distribution == "Ubuntu"
          - ansible_distribution_version == "22.04"
        fail_msg: "Skipping non-Ubuntu 22.04 system"
      
    - name: Check current kernel
      command: uname -r
      register: current_kernel
      changed_when: false
      
    - name: Check if kernel packages are held
      shell: dpkg --get-selections | grep -E "linux-(image|headers|modules|modules-extra)-{{ target_kernel }}-generic" | grep hold | wc -l
      register: held_count
      changed_when: false
      
    - name: Set skip flag
      set_fact:
        should_skip: "{{ (target_kernel in current_kernel.stdout) and (held_count.stdout | int >= 4) }}"
      
    - name: Skip if already fixed (kernel OK and packages held)
      meta: end_host
      when: should_skip | bool
      
    - name: Install kernel packages (if not already on target)
      apt:
        name:
          - "linux-image-{{ target_kernel }}-generic"
          - "linux-headers-{{ target_kernel }}-generic"
          - "linux-modules-{{ target_kernel }}-generic"
          - "linux-modules-extra-{{ target_kernel }}-generic"
        state: present
        update_cache: yes
        allow_downgrade: yes
      when: target_kernel not in current_kernel.stdout
        
    - name: Hold kernel packages
      dpkg_selections:
        name: "{{ item }}"
        selection: hold
      loop:
        - "linux-image-{{ target_kernel }}-generic"
        - "linux-headers-{{ target_kernel }}-generic"
        - "linux-modules-{{ target_kernel }}-generic"
        - "linux-modules-extra-{{ target_kernel }}-generic"
        
    - name: Update GRUB
      command: update-grub
      when: target_kernel not in current_kernel.stdout

    - name: Manual GRUB configuration required
      debug:
        msg: |
          IMPORTANT: Manual GRUB configuration required to complete kernel fix.
          After playbook completion, run these commands:
          1. Check GRUB menu: grep menuentry /boot/grub/grub.cfg | grep "5.15.0"
          2. Set correct GRUB_DEFAULT: sudo sed -i 's/GRUB_DEFAULT=0/GRUB_DEFAULT="1>X"/' /etc/default/grub
             (Replace X with correct submenu index based on menu output)
          3. Update GRUB: sudo update-grub
          4. Reboot: sudo reboot
          5. Verify: uname -r (should show {{ target_kernel }}-generic)
      when: target_kernel not in current_kernel.stdout
      
    - name: Display status for hosts already on target kernel
      debug:
        msg: "Host already on kernel {{ target_kernel }}, only applied package hold"
      when: target_kernel in current_kernel.stdout
      
    - name: Check if Lacework datacollector service exists
      systemd:
        name: datacollector
      register: lacework_service
      failed_when: false
      changed_when: false
      
    - name: Stop and disable Lacework datacollector service
      systemd:
        name: datacollector
        state: stopped
        enabled: no
        masked: yes
      when:
        - lacework_service.status is defined
        - lacework_service.status.ActiveState is defined
      register: lacework_disabled
      
    - name: Display Lacework status
      debug:
        msg: "Lacework datacollector has been disabled and masked. It will be deployed via Helm in the future."
      when: lacework_disabled.changed | default(false)